import 'dart:convert';
import 'dart:io';
import 'package:path_provider/path_provider.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart' as path;
import '../models/consultation.dart';

class StorageService {
  static final StorageService _instance = StorageService._internal();
  static StorageService get instance => _instance;
  StorageService._internal();

  static const String _dbName = 'doc_transcribe.db';
  static const String _consultationsTable = 'consultations';
  static const String _settingsTable = 'settings';

  Database? _database;
  Directory? _appDirectory;
  Directory? _documentsDirectory;
  Directory? _audioDirectory;
  Directory? _pdfDirectory;

  static const FlutterSecureStorage _secureStorage = FlutterSecureStorage(
    aOptions: AndroidOptions(
      encryptedSharedPreferences: true,
    ),
    iOptions: IOSOptions(
      accessibility: KeychainAccessibility.first_unlock_this_device,
    ),
  );

  // Initialize storage service
  Future<void> initialize() async {
    await _initializeDirectories();
    await _initializeDatabase();
  }

  // Initialize app directories
  Future<void> _initializeDirectories() async {
    _appDirectory = await getApplicationDocumentsDirectory();
    _documentsDirectory = Directory(path.join(_appDirectory!.path, 'DocTranscribe'));
    _audioDirectory = Directory(path.join(_documentsDirectory!.path, 'audio'));
    _pdfDirectory = Directory(path.join(_documentsDirectory!.path, 'pdfs'));

    // Create directories if they don't exist
    await _documentsDirectory!.create(recursive: true);
    await _audioDirectory!.create(recursive: true);
    await _pdfDirectory!.create(recursive: true);
  }

  // Initialize SQLite database
  Future<void> _initializeDatabase() async {
    final dbPath = path.join(_appDirectory!.path, _dbName);
    
    _database = await openDatabase(
      dbPath,
      version: 1,
      onCreate: _createDatabase,
      onUpgrade: _upgradeDatabase,
    );
  }

  Future<void> _createDatabase(Database db, int version) async {
    // Create consultations table
    await db.execute('''
      CREATE TABLE $_consultationsTable (
        id TEXT PRIMARY KEY,
        patient_data TEXT NOT NULL,
        transcript_data TEXT,
        created_at TEXT NOT NULL,
        completed_at TEXT,
        status TEXT NOT NULL,
        audio_file_path TEXT,
        pdf_file_path TEXT,
        doctor_name TEXT,
        error_message TEXT
      )
    ''');

    // Create settings table
    await db.execute('''
      CREATE TABLE $_settingsTable (
        key TEXT PRIMARY KEY,
        value TEXT NOT NULL
      )
    ''');
  }

  Future<void> _upgradeDatabase(Database db, int oldVersion, int newVersion) async {
    // Handle database upgrades here
  }

  // Consultation CRUD operations
  Future<void> saveConsultation(Consultation consultation) async {
    final db = _database!;
    
    await db.insert(
      _consultationsTable,
      {
        'id': consultation.id,
        'patient_data': jsonEncode(consultation.patient.toJson()),
        'transcript_data': consultation.transcript != null 
            ? jsonEncode(consultation.transcript!.toJson()) 
            : null,
        'created_at': consultation.createdAt.toIso8601String(),
        'completed_at': consultation.completedAt?.toIso8601String(),
        'status': consultation.status.name,
        'audio_file_path': consultation.audioFilePath,
        'pdf_file_path': consultation.pdfFilePath,
        'doctor_name': consultation.doctorName,
        'error_message': consultation.errorMessage,
      },
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  Future<Consultation?> getConsultation(String id) async {
    final db = _database!;
    
    final results = await db.query(
      _consultationsTable,
      where: 'id = ?',
      whereArgs: [id],
    );

    if (results.isEmpty) return null;

    return _consultationFromMap(results.first);
  }

  Future<List<Consultation>> getAllConsultations() async {
    final db = _database!;
    
    final results = await db.query(
      _consultationsTable,
      orderBy: 'created_at DESC',
    );

    return results.map(_consultationFromMap).toList();
  }

  Future<void> deleteConsultation(String id) async {
    final db = _database!;
    
    // Get consultation to delete associated files
    final consultation = await getConsultation(id);
    if (consultation != null) {
      // Delete audio file
      if (consultation.audioFilePath != null) {
        final audioFile = File(consultation.audioFilePath!);
        if (await audioFile.exists()) {
          await audioFile.delete();
        }
      }

      // Delete PDF file
      if (consultation.pdfFilePath != null) {
        final pdfFile = File(consultation.pdfFilePath!);
        if (await pdfFile.exists()) {
          await pdfFile.delete();
        }
      }
    }

    await db.delete(
      _consultationsTable,
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  Consultation _consultationFromMap(Map<String, dynamic> map) {
    return Consultation.fromJson({
      'id': map['id'],
      'patient': jsonDecode(map['patient_data']),
      'transcript': map['transcript_data'] != null 
          ? jsonDecode(map['transcript_data']) 
          : null,
      'createdAt': map['created_at'],
      'completedAt': map['completed_at'],
      'status': map['status'],
      'audioFilePath': map['audio_file_path'],
      'pdfFilePath': map['pdf_file_path'],
      'doctorName': map['doctor_name'],
      'errorMessage': map['error_message'],
    });
  }

  // Settings management
  Future<void> saveSetting(String key, String value) async {
    final db = _database!;
    
    await db.insert(
      _settingsTable,
      {'key': key, 'value': value},
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  Future<String?> getSetting(String key) async {
    final db = _database!;
    
    final results = await db.query(
      _settingsTable,
      where: 'key = ?',
      whereArgs: [key],
    );

    if (results.isEmpty) return null;
    return results.first['value'] as String;
  }

  // Secure storage for sensitive data
  Future<void> saveSecureData(String key, String value) async {
    await _secureStorage.write(key: key, value: value);
  }

  Future<String?> getSecureData(String key) async {
    return await _secureStorage.read(key: key);
  }

  Future<void> deleteSecureData(String key) async {
    await _secureStorage.delete(key: key);
  }

  // File path helpers
  String getAudioFilePath(String consultationId) {
    return path.join(_audioDirectory!.path, '$consultationId.wav');
  }

  String getPdfFilePath(String consultationId, String patientName) {
    final timestamp = DateTime.now().toIso8601String().replaceAll(':', '-');
    final fileName = '${patientName.replaceAll(' ', '_')}_$timestamp.pdf';
    return path.join(_pdfDirectory!.path, fileName);
  }

  Directory get documentsDirectory => _documentsDirectory!;
  Directory get audioDirectory => _audioDirectory!;
  Directory get pdfDirectory => _pdfDirectory!;

  // Cleanup
  Future<void> cleanup() async {
    await _database?.close();
  }
}
