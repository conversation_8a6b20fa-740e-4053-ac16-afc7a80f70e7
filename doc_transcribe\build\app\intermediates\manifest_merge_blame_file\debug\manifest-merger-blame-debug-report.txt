1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.doc_transcribe"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
8-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml
9        android:targetSdkVersion="34" />
9-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml
10
11    <!-- Permissions -->
12    <uses-permission android:name="android.permission.INTERNET" />
12-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:4:5-67
12-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:4:22-64
13    <uses-permission android:name="android.permission.RECORD_AUDIO" />
13-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:5:5-71
13-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:5:22-68
14    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
14-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:6:5-81
14-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:6:22-78
15    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
15-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:7:5-80
15-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:7:22-77
16    <uses-permission android:name="android.permission.WAKE_LOCK" />
16-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:8:5-68
16-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:8:22-65
17    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
17-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:9:5-77
17-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:9:22-74
18
19    <permission
19-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\36e2c28c5ed8faddcbd47d21cdfed089\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
20        android:name="com.example.doc_transcribe.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
20-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\36e2c28c5ed8faddcbd47d21cdfed089\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
21        android:protectionLevel="signature" />
21-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\36e2c28c5ed8faddcbd47d21cdfed089\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
22
23    <uses-permission android:name="com.example.doc_transcribe.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
23-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\36e2c28c5ed8faddcbd47d21cdfed089\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
23-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\36e2c28c5ed8faddcbd47d21cdfed089\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
24
25    <application
26        android:name="android.app.Application"
26-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:13:9-42
27        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
27-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\36e2c28c5ed8faddcbd47d21cdfed089\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
28        android:debuggable="true"
29        android:icon="@mipmap/ic_launcher"
29-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:14:9-43
30        android:label="DocTranscribe"
30-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:12:9-38
31        android:requestLegacyExternalStorage="true" >
31-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:15:9-52
32        <activity
32-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:17:9-39:20
33            android:name="com.example.doc_transcribe.MainActivity"
33-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:18:13-41
34            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
34-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:22:13-163
35            android:exported="true"
35-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:19:13-36
36            android:hardwareAccelerated="true"
36-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:23:13-47
37            android:launchMode="singleTop"
37-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:20:13-43
38            android:theme="@style/LaunchTheme"
38-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:21:13-47
39            android:windowSoftInputMode="adjustResize" >
39-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:24:13-55
40
41            <!--
42                 Specifies an Android theme to apply to this Activity as soon as
43                 the Android process has started. This theme is visible to the user
44                 while the Flutter UI initializes. After that, this theme continues
45                 to determine the Window background behind the Flutter UI.
46            -->
47            <meta-data
47-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:30:13-33:17
48                android:name="io.flutter.embedding.android.NormalTheme"
48-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:31:15-70
49                android:resource="@style/NormalTheme" />
49-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:32:15-52
50
51            <intent-filter android:autoVerify="true" >
51-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:35:13-38:29
51-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:35:28-53
52                <action android:name="android.intent.action.MAIN" />
52-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:36:17-68
52-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:36:25-66
53
54                <category android:name="android.intent.category.LAUNCHER" />
54-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:37:17-76
54-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:37:27-74
55            </intent-filter>
56        </activity>
57
58        <!--
59             Don't delete the meta-data below.
60             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
61        -->
62        <meta-data
62-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:43:9-45:33
63            android:name="flutterEmbedding"
63-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:44:13-44
64            android:value="2" />
64-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:45:13-30
65
66        <!-- File provider for sharing files -->
67        <provider
68            android:name="androidx.core.content.FileProvider"
68-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:49:13-62
69            android:authorities="com.example.doc_transcribe.fileprovider"
69-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:50:13-64
70            android:exported="false"
70-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:51:13-37
71            android:grantUriPermissions="true" >
71-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:52:13-47
72            <meta-data
72-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:53:13-55:54
73                android:name="android.support.FILE_PROVIDER_PATHS"
73-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:54:17-67
74                android:resource="@xml/file_paths" />
74-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:55:17-51
75        </provider>
76
77        <uses-library
77-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d88497c41e4f38cb083ab56ea6c34eba\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
78            android:name="androidx.window.extensions"
78-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d88497c41e4f38cb083ab56ea6c34eba\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
79            android:required="false" />
79-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d88497c41e4f38cb083ab56ea6c34eba\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
80        <uses-library
80-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d88497c41e4f38cb083ab56ea6c34eba\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
81            android:name="androidx.window.sidecar"
81-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d88497c41e4f38cb083ab56ea6c34eba\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
82            android:required="false" />
82-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d88497c41e4f38cb083ab56ea6c34eba\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
83
84        <provider
84-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\43a6bfef74347e360d9addd8a47d3fea\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
85            android:name="androidx.startup.InitializationProvider"
85-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\43a6bfef74347e360d9addd8a47d3fea\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:25:13-67
86            android:authorities="com.example.doc_transcribe.androidx-startup"
86-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\43a6bfef74347e360d9addd8a47d3fea\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:26:13-68
87            android:exported="false" >
87-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\43a6bfef74347e360d9addd8a47d3fea\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:27:13-37
88            <meta-data
88-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\43a6bfef74347e360d9addd8a47d3fea\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
89                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
89-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\43a6bfef74347e360d9addd8a47d3fea\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
90                android:value="androidx.startup" />
90-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\43a6bfef74347e360d9addd8a47d3fea\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
91            <meta-data
91-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\11361ffd419190fe575367e107bdcce6\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
92                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
92-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\11361ffd419190fe575367e107bdcce6\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
93                android:value="androidx.startup" />
93-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\11361ffd419190fe575367e107bdcce6\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
94        </provider>
95
96        <receiver
96-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\11361ffd419190fe575367e107bdcce6\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
97            android:name="androidx.profileinstaller.ProfileInstallReceiver"
97-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\11361ffd419190fe575367e107bdcce6\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
98            android:directBootAware="false"
98-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\11361ffd419190fe575367e107bdcce6\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
99            android:enabled="true"
99-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\11361ffd419190fe575367e107bdcce6\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
100            android:exported="true"
100-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\11361ffd419190fe575367e107bdcce6\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
101            android:permission="android.permission.DUMP" >
101-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\11361ffd419190fe575367e107bdcce6\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
102            <intent-filter>
102-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\11361ffd419190fe575367e107bdcce6\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
103                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
103-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\11361ffd419190fe575367e107bdcce6\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
103-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\11361ffd419190fe575367e107bdcce6\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
104            </intent-filter>
105            <intent-filter>
105-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\11361ffd419190fe575367e107bdcce6\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
106                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
106-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\11361ffd419190fe575367e107bdcce6\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
106-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\11361ffd419190fe575367e107bdcce6\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
107            </intent-filter>
108            <intent-filter>
108-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\11361ffd419190fe575367e107bdcce6\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
109                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
109-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\11361ffd419190fe575367e107bdcce6\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
109-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\11361ffd419190fe575367e107bdcce6\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
110            </intent-filter>
111            <intent-filter>
111-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\11361ffd419190fe575367e107bdcce6\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
112                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
112-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\11361ffd419190fe575367e107bdcce6\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
112-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\11361ffd419190fe575367e107bdcce6\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
113            </intent-filter>
114        </receiver>
115    </application>
116
117</manifest>
