1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.doc_transcribe"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="23"
8-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml
9        android:targetSdkVersion="34" />
9-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml
10
11    <!-- Permissions -->
12    <uses-permission android:name="android.permission.INTERNET" />
12-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:4:5-67
12-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:4:22-64
13    <uses-permission android:name="android.permission.RECORD_AUDIO" />
13-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:5:5-71
13-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:5:22-68
14    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
14-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:6:5-81
14-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:6:22-78
15    <uses-permission
15-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:7:5-80
16        android:name="android.permission.READ_EXTERNAL_STORAGE"
16-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:7:22-77
17        android:maxSdkVersion="32" />
17-->[:file_picker] C:\python_programs\DocScribe\doc_transcribe\build\file_picker\intermediates\merged_manifest\debug\AndroidManifest.xml:11:9-35
18    <uses-permission android:name="android.permission.WAKE_LOCK" />
18-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:8:5-68
18-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:8:22-65
19    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
19-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:9:5-77
19-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:9:22-74
20
21    <queries>
21-->[:file_picker] C:\python_programs\DocScribe\doc_transcribe\build\file_picker\intermediates\merged_manifest\debug\AndroidManifest.xml:13:5-19:15
22        <intent>
22-->[:file_picker] C:\python_programs\DocScribe\doc_transcribe\build\file_picker\intermediates\merged_manifest\debug\AndroidManifest.xml:14:9-18:18
23            <action android:name="android.intent.action.GET_CONTENT" />
23-->[:file_picker] C:\python_programs\DocScribe\doc_transcribe\build\file_picker\intermediates\merged_manifest\debug\AndroidManifest.xml:15:13-72
23-->[:file_picker] C:\python_programs\DocScribe\doc_transcribe\build\file_picker\intermediates\merged_manifest\debug\AndroidManifest.xml:15:21-69
24
25            <data android:mimeType="*/*" />
25-->[:file_picker] C:\python_programs\DocScribe\doc_transcribe\build\file_picker\intermediates\merged_manifest\debug\AndroidManifest.xml:17:13-44
25-->[:file_picker] C:\python_programs\DocScribe\doc_transcribe\build\file_picker\intermediates\merged_manifest\debug\AndroidManifest.xml:17:19-41
26        </intent>
27    </queries>
28
29    <permission
29-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\b84429f70a7457aa890ea440bb20b385\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
30        android:name="com.example.doc_transcribe.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
30-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\b84429f70a7457aa890ea440bb20b385\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
31        android:protectionLevel="signature" />
31-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\b84429f70a7457aa890ea440bb20b385\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
32
33    <uses-permission android:name="com.example.doc_transcribe.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
33-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\b84429f70a7457aa890ea440bb20b385\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
33-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\b84429f70a7457aa890ea440bb20b385\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
34
35    <application
36        android:name="android.app.Application"
36-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:13:9-42
37        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
37-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\b84429f70a7457aa890ea440bb20b385\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
38        android:debuggable="true"
39        android:extractNativeLibs="false"
40        android:icon="@mipmap/ic_launcher"
40-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:14:9-43
41        android:label="DocTranscribe"
41-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:12:9-38
42        android:requestLegacyExternalStorage="true" >
42-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:15:9-52
43        <activity
43-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:17:9-39:20
44            android:name="com.example.doc_transcribe.MainActivity"
44-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:18:13-41
45            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
45-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:22:13-163
46            android:exported="true"
46-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:19:13-36
47            android:hardwareAccelerated="true"
47-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:23:13-47
48            android:launchMode="singleTop"
48-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:20:13-43
49            android:theme="@style/LaunchTheme"
49-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:21:13-47
50            android:windowSoftInputMode="adjustResize" >
50-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:24:13-55
51
52            <!--
53                 Specifies an Android theme to apply to this Activity as soon as
54                 the Android process has started. This theme is visible to the user
55                 while the Flutter UI initializes. After that, this theme continues
56                 to determine the Window background behind the Flutter UI.
57            -->
58            <meta-data
58-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:30:13-33:17
59                android:name="io.flutter.embedding.android.NormalTheme"
59-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:31:15-70
60                android:resource="@style/NormalTheme" />
60-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:32:15-52
61
62            <intent-filter android:autoVerify="true" >
62-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:35:13-38:29
62-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:35:28-53
63                <action android:name="android.intent.action.MAIN" />
63-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:36:17-68
63-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:36:25-66
64
65                <category android:name="android.intent.category.LAUNCHER" />
65-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:37:17-76
65-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:37:27-74
66            </intent-filter>
67        </activity>
68
69        <!--
70             Don't delete the meta-data below.
71             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
72        -->
73        <meta-data
73-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:43:9-45:33
74            android:name="flutterEmbedding"
74-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:44:13-44
75            android:value="2" />
75-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:45:13-30
76
77        <!-- File provider for sharing files -->
78        <provider
79            android:name="androidx.core.content.FileProvider"
79-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:49:13-62
80            android:authorities="com.example.doc_transcribe.fileprovider"
80-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:50:13-64
81            android:exported="false"
81-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:51:13-37
82            android:grantUriPermissions="true" >
82-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:52:13-47
83            <meta-data
83-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:53:13-55:54
84                android:name="android.support.FILE_PROVIDER_PATHS"
84-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:54:17-67
85                android:resource="@xml/file_paths" />
85-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:55:17-51
86        </provider>
87        <provider
87-->[:printing] C:\python_programs\DocScribe\doc_transcribe\build\printing\intermediates\merged_manifest\debug\AndroidManifest.xml:10:9-18:20
88            android:name="net.nfet.flutter.printing.PrintFileProvider"
88-->[:printing] C:\python_programs\DocScribe\doc_transcribe\build\printing\intermediates\merged_manifest\debug\AndroidManifest.xml:11:13-71
89            android:authorities="com.example.doc_transcribe.flutter.printing"
89-->[:printing] C:\python_programs\DocScribe\doc_transcribe\build\printing\intermediates\merged_manifest\debug\AndroidManifest.xml:12:13-68
90            android:exported="false"
90-->[:printing] C:\python_programs\DocScribe\doc_transcribe\build\printing\intermediates\merged_manifest\debug\AndroidManifest.xml:13:13-37
91            android:grantUriPermissions="true" >
91-->[:printing] C:\python_programs\DocScribe\doc_transcribe\build\printing\intermediates\merged_manifest\debug\AndroidManifest.xml:14:13-47
92            <meta-data
92-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:53:13-55:54
93                android:name="android.support.FILE_PROVIDER_PATHS"
93-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:54:17-67
94                android:resource="@xml/flutter_printing_file_paths" />
94-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:55:17-51
95        </provider>
96        <!--
97           Declares a provider which allows us to store files to share in
98           '.../caches/share_plus' and grant the receiving action access
99        -->
100        <provider
100-->[:share_plus] C:\python_programs\DocScribe\doc_transcribe\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:15:9-23:20
101            android:name="dev.fluttercommunity.plus.share.ShareFileProvider"
101-->[:share_plus] C:\python_programs\DocScribe\doc_transcribe\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:16:13-77
102            android:authorities="com.example.doc_transcribe.flutter.share_provider"
102-->[:share_plus] C:\python_programs\DocScribe\doc_transcribe\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:17:13-74
103            android:exported="false"
103-->[:share_plus] C:\python_programs\DocScribe\doc_transcribe\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:18:13-37
104            android:grantUriPermissions="true" >
104-->[:share_plus] C:\python_programs\DocScribe\doc_transcribe\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:19:13-47
105            <meta-data
105-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:53:13-55:54
106                android:name="android.support.FILE_PROVIDER_PATHS"
106-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:54:17-67
107                android:resource="@xml/flutter_share_file_paths" />
107-->C:\python_programs\DocScribe\doc_transcribe\android\app\src\main\AndroidManifest.xml:55:17-51
108        </provider>
109        <!--
110           This manifest declared broadcast receiver allows us to use an explicit
111           Intent when creating a PendingItent to be informed of the user's choice
112        -->
113        <receiver
113-->[:share_plus] C:\python_programs\DocScribe\doc_transcribe\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:28:9-34:20
114            android:name="dev.fluttercommunity.plus.share.SharePlusPendingIntent"
114-->[:share_plus] C:\python_programs\DocScribe\doc_transcribe\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:29:13-82
115            android:exported="false" >
115-->[:share_plus] C:\python_programs\DocScribe\doc_transcribe\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:30:13-37
116            <intent-filter>
116-->[:share_plus] C:\python_programs\DocScribe\doc_transcribe\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:31:13-33:29
117                <action android:name="EXTRA_CHOSEN_COMPONENT" />
117-->[:share_plus] C:\python_programs\DocScribe\doc_transcribe\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:32:17-65
117-->[:share_plus] C:\python_programs\DocScribe\doc_transcribe\build\share_plus\intermediates\merged_manifest\debug\AndroidManifest.xml:32:25-62
118            </intent-filter>
119        </receiver>
120
121        <uses-library
121-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\68656057c0f9df10db2245ba342a2616\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
122            android:name="androidx.window.extensions"
122-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\68656057c0f9df10db2245ba342a2616\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
123            android:required="false" />
123-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\68656057c0f9df10db2245ba342a2616\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
124        <uses-library
124-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\68656057c0f9df10db2245ba342a2616\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
125            android:name="androidx.window.sidecar"
125-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\68656057c0f9df10db2245ba342a2616\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
126            android:required="false" />
126-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\68656057c0f9df10db2245ba342a2616\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
127
128        <provider
128-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\c5ce1127f562a21d56d5086b663c7f0e\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
129            android:name="androidx.startup.InitializationProvider"
129-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\c5ce1127f562a21d56d5086b663c7f0e\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:25:13-67
130            android:authorities="com.example.doc_transcribe.androidx-startup"
130-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\c5ce1127f562a21d56d5086b663c7f0e\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:26:13-68
131            android:exported="false" >
131-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\c5ce1127f562a21d56d5086b663c7f0e\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:27:13-37
132            <meta-data
132-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\c5ce1127f562a21d56d5086b663c7f0e\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
133                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
133-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\c5ce1127f562a21d56d5086b663c7f0e\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
134                android:value="androidx.startup" />
134-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\c5ce1127f562a21d56d5086b663c7f0e\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
135            <meta-data
135-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\be612b59f18afe430fed3f73124efcee\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
136                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
136-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\be612b59f18afe430fed3f73124efcee\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
137                android:value="androidx.startup" />
137-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\be612b59f18afe430fed3f73124efcee\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
138        </provider>
139
140        <receiver
140-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\be612b59f18afe430fed3f73124efcee\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
141            android:name="androidx.profileinstaller.ProfileInstallReceiver"
141-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\be612b59f18afe430fed3f73124efcee\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
142            android:directBootAware="false"
142-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\be612b59f18afe430fed3f73124efcee\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
143            android:enabled="true"
143-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\be612b59f18afe430fed3f73124efcee\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
144            android:exported="true"
144-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\be612b59f18afe430fed3f73124efcee\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
145            android:permission="android.permission.DUMP" >
145-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\be612b59f18afe430fed3f73124efcee\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
146            <intent-filter>
146-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\be612b59f18afe430fed3f73124efcee\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
147                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
147-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\be612b59f18afe430fed3f73124efcee\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
147-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\be612b59f18afe430fed3f73124efcee\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
148            </intent-filter>
149            <intent-filter>
149-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\be612b59f18afe430fed3f73124efcee\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
150                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
150-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\be612b59f18afe430fed3f73124efcee\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
150-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\be612b59f18afe430fed3f73124efcee\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
151            </intent-filter>
152            <intent-filter>
152-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\be612b59f18afe430fed3f73124efcee\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
153                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
153-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\be612b59f18afe430fed3f73124efcee\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
153-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\be612b59f18afe430fed3f73124efcee\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
154            </intent-filter>
155            <intent-filter>
155-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\be612b59f18afe430fed3f73124efcee\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
156                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
156-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\be612b59f18afe430fed3f73124efcee\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
156-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\be612b59f18afe430fed3f73124efcee\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
157            </intent-filter>
158        </receiver>
159    </application>
160
161</manifest>
