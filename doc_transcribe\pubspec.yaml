name: doc_transcribe
description: A Flutter app for doctors to record, transcribe, and generate PDF reports of patient consultations.
publish_to: 'none'
version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'
  flutter: ">=3.0.0"

dependencies:
  flutter:
    sdk: flutter
  cupertino_icons: ^1.0.6

  # State Management
  provider: ^6.1.1

  # Audio Recording
  record: ^5.0.4
  permission_handler: ^11.3.0
  path_provider: ^2.1.2

  # PDF Generation
  pdf: ^3.10.7
  printing: ^5.12.0

  # Local Storage
  sqflite: ^2.3.2
  shared_preferences: ^2.2.2

  # File Handling
  share_plus: ^7.2.2
  file_picker: ^6.1.1

  # UI Components
  intl: ^0.19.0
  uuid: ^4.3.3

  # HTTP & Networking (for NVIDIA Riva)
  http: ^1.2.0
  web_socket_channel: ^2.4.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.1

flutter:
  uses-material-design: true

  assets:
    - assets/images/
